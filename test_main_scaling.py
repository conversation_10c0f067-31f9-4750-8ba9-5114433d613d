#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序的等比例缩放功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径，以便导入main_gui
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scaling():
    """测试不同屏幕大小下的缩放效果"""
    
    # 模拟不同的屏幕分辨率
    test_resolutions = [
        (1024, 600, "极小屏幕 (1024x600)"),
        (1280, 720, "小屏幕 (1280x720)"),
        (1366, 768, "标准屏幕 (1366x768)"),
        (1920, 1080, "大屏幕 (1920x1080)")
    ]
    
    print("等比例缩放测试")
    print("=" * 50)
    
    for width, height, desc in test_resolutions:
        print(f"\n{desc}:")
        
        # 计算缩放比例
        base_width = 1366
        base_height = 768
        
        width_scale = width / base_width
        height_scale = height / base_height
        scale_factor = min(width_scale, height_scale)
        
        # 检查屏幕类型
        is_small_screen = width < 1366 or height < 768
        is_very_small_screen = width <= 1024 or height <= 600
        
        # 对于小屏幕的特殊处理
        if is_very_small_screen:
            min_scale = min(width / 1000, height / 600)
            scale_factor = min(scale_factor, min_scale)
        elif is_small_screen:
            min_scale = min(width / 1200, height / 700)
            scale_factor = min(scale_factor, min_scale)
        
        # 限制缩放范围
        scale_factor = max(0.3, min(scale_factor, 2.0))
        
        print(f"  分辨率: {width}x{height}")
        print(f"  缩放比例: {scale_factor:.2f} ({scale_factor:.0%})")
        print(f"  小屏幕模式: {'是' if is_small_screen else '否'}")
        print(f"  极小屏幕模式: {'是' if is_very_small_screen else '否'}")
        
        # 计算组件尺寸
        def scale_value(value):
            return int(value * scale_factor)
        
        # 测试按钮尺寸
        if is_very_small_screen:
            button_layout = "垂直排列"
        else:
            button_layout = "水平排列"
        
        # 测试文本框尺寸
        if is_very_small_screen:
            text_height = 2
            text_width = 20
        elif is_small_screen:
            text_height = 3
            text_width = 25
        else:
            text_height = max(3, int(4 * scale_factor))
            text_width = max(25, int(40 * scale_factor))
        
        # 测试Entry宽度
        if is_very_small_screen:
            entry_width = max(15, int(20 * scale_factor))
        elif is_small_screen:
            entry_width = max(20, int(25 * scale_factor))
        else:
            entry_width = max(25, int(35 * scale_factor))
        
        print(f"  按钮布局: {button_layout}")
        print(f"  文本框尺寸: {text_height}行 x {text_width}列")
        print(f"  输入框宽度: {entry_width}字符")
        print(f"  间距缩放: {scale_value(10)}px (原始10px)")

def create_demo_window():
    """创建演示窗口"""
    root = tk.Tk()
    root.title("等比例缩放演示")
    
    # 获取屏幕信息
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # 计算缩放
    base_width = 1366
    base_height = 768
    width_scale = screen_width / base_width
    height_scale = screen_height / base_height
    scale_factor = min(width_scale, height_scale)
    
    is_small_screen = screen_width < 1366 or screen_height < 768
    is_very_small_screen = screen_width <= 1024 or screen_height <= 600
    
    if is_very_small_screen:
        min_scale = min(screen_width / 1000, screen_height / 600)
        scale_factor = min(scale_factor, min_scale)
    elif is_small_screen:
        min_scale = min(screen_width / 1200, screen_height / 700)
        scale_factor = min(scale_factor, min_scale)
    
    scale_factor = max(0.3, min(scale_factor, 2.0))
    
    def scale_value(value):
        return int(value * scale_factor)
    
    # 设置窗口大小
    if is_very_small_screen:
        window_width = int(800 * scale_factor)
        window_height = int(500 * scale_factor)
    else:
        window_width = int(1000 * scale_factor)
        window_height = int(600 * scale_factor)
    
    window_width = min(window_width, int(screen_width * 0.95))
    window_height = min(window_height, int(screen_height * 0.95))
    
    x = (screen_width - window_width) // 2
    y = (screen_height - window_height) // 2
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    # 创建界面
    main_frame = ttk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=scale_value(10), pady=scale_value(10))
    
    # 信息显示
    info_text = f"""当前屏幕: {screen_width}x{screen_height}
缩放比例: {scale_factor:.2f} ({scale_factor:.0%})
小屏幕模式: {'是' if is_small_screen else '否'}
极小屏幕模式: {'是' if is_very_small_screen else '否'}
窗口大小: {window_width}x{window_height}"""
    
    info_label = ttk.Label(main_frame, text=info_text, font=('SimHei', max(8, int(10 * scale_factor))))
    info_label.pack(pady=scale_value(10))
    
    # 模拟添加病例区域
    add_frame = ttk.LabelFrame(main_frame, text="添加新病例 (演示)", padding=scale_value(8))
    add_frame.pack(fill='both', expand=True, pady=scale_value(5))
    
    # 输入字段
    fields = ['姓名', '年龄', '性别', '住址']
    for i, field in enumerate(fields):
        ttk.Label(add_frame, text=f"{field}:", font=('SimHei', max(8, int(9 * scale_factor)))).grid(
            row=i, column=0, sticky='w', padx=(0, scale_value(5)), pady=scale_value(2))
        
        if is_very_small_screen:
            entry_width = max(15, int(20 * scale_factor))
        elif is_small_screen:
            entry_width = max(20, int(25 * scale_factor))
        else:
            entry_width = max(25, int(35 * scale_factor))
        
        entry = ttk.Entry(add_frame, width=entry_width)
        entry.grid(row=i, column=1, sticky='ew', pady=scale_value(2))
    
    add_frame.columnconfigure(1, weight=1)
    
    # 按钮区域
    button_frame = ttk.Frame(add_frame)
    button_frame.grid(row=len(fields), column=0, columnspan=2, pady=scale_value(10), sticky='ew')
    
    if is_very_small_screen:
        # 垂直排列
        ttk.Button(button_frame, text="添加病例").pack(side='top', fill='x', pady=(0, scale_value(3)))
        ttk.Button(button_frame, text="清空输入").pack(side='top', fill='x')
    else:
        # 水平排列
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        ttk.Button(button_frame, text="添加病例").grid(row=0, column=0, sticky='ew', padx=(0, scale_value(5)))
        ttk.Button(button_frame, text="清空输入").grid(row=0, column=1, sticky='ew')
    
    root.mainloop()

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 控制台测试 (显示计算结果)")
    print("2. 图形界面演示")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        test_scaling()
    elif choice == "2":
        create_demo_window()
    else:
        print("无效选择，运行控制台测试...")
        test_scaling()
