#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试无限制搜索结果功能
"""

def test_search_similar_cases():
    """测试搜索相似病例方法的修改"""
    
    # 模拟搜索方法的新签名
    def search_similar_cases(name: str, gender: str, address: str, symptoms: str, condition: str, min_similarity: float = 0.3):
        """
        搜索相似病例（无数量限制版本）
        
        参数:
        - name: 姓名
        - gender: 性别  
        - address: 住址
        - symptoms: 症状
        - condition: 病情
        - min_similarity: 最小相似度阈值（默认0.3）
        
        返回:
        - 所有匹配的病例，按相似度从高到低排序
        """
        
        # 模拟病例数据
        all_records = [
            {'name': '张三', 'symptoms': '口渴', 'similarity': 0.9},
            {'name': '李四', 'symptoms': '口渴多饮', 'similarity': 0.8},
            {'name': '王五', 'symptoms': '口干', 'similarity': 0.7},
            {'name': '赵六', 'symptoms': '多尿', 'similarity': 0.6},
            {'name': '钱七', 'symptoms': '乏力', 'similarity': 0.5},
            {'name': '孙八', 'symptoms': '头晕', 'similarity': 0.4},
            {'name': '周九', 'symptoms': '口渴乏力', 'similarity': 0.9},
            {'name': '吴十', 'symptoms': '多饮多尿', 'similarity': 0.8},
            {'name': '郑十一', 'symptoms': '口干舌燥', 'similarity': 0.7},
            {'name': '王十二', 'symptoms': '疲劳', 'similarity': 0.4},
            {'name': '李十三', 'symptoms': '口渴头晕', 'similarity': 0.8},
            {'name': '张十四', 'symptoms': '多饮', 'similarity': 0.6},
            {'name': '刘十五', 'symptoms': '乏力头晕', 'similarity': 0.5},
        ]
        
        # 过滤符合最小相似度的记录
        similar_cases = []
        for record in all_records:
            if record['similarity'] >= min_similarity:
                similar_cases.append((record, record['similarity']))
        
        # 按相似度排序（从高到低）
        similar_cases.sort(key=lambda x: x[1], reverse=True)
        
        # 返回所有匹配的结果（不再限制数量）
        return similar_cases
    
    # 测试搜索
    print("=== 测试无限制搜索结果 ===")
    
    # 测试1：搜索口渴相关症状
    print("\n1. 搜索症状包含'口渴'的病例：")
    results = search_similar_cases("", "", "", "口渴", "", min_similarity=0.3)
    print(f"找到 {len(results)} 条结果：")
    for i, (record, similarity) in enumerate(results, 1):
        print(f"  {i}. {record['name']} - {record['symptoms']} (相似度: {similarity:.1f})")
    
    # 测试2：提高相似度阈值
    print("\n2. 搜索相似度 >= 0.7 的病例：")
    results = search_similar_cases("", "", "", "口渴", "", min_similarity=0.7)
    print(f"找到 {len(results)} 条结果：")
    for i, (record, similarity) in enumerate(results, 1):
        print(f"  {i}. {record['name']} - {record['symptoms']} (相似度: {similarity:.1f})")
    
    # 测试3：验证排序
    print("\n3. 验证结果按相似度排序（从高到低）：")
    results = search_similar_cases("", "", "", "", "", min_similarity=0.3)
    print(f"所有 {len(results)} 条结果的相似度排序：")
    for i, (record, similarity) in enumerate(results, 1):
        print(f"  {i}. {record['name']} (相似度: {similarity:.1f})")
    
    # 验证排序是否正确
    similarities = [similarity for _, similarity in results]
    is_sorted = all(similarities[i] >= similarities[i+1] for i in range(len(similarities)-1))
    print(f"\n排序验证: {'✓ 正确' if is_sorted else '✗ 错误'}")
    
    print("\n=== 修改总结 ===")
    print("✓ 移除了 max_results=10 的限制")
    print("✓ 返回所有符合条件的搜索结果")
    print("✓ 保持按相似度从高到低排序")
    print("✓ 最相关的结果显示在前面")

if __name__ == "__main__":
    test_search_similar_cases()
