#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

class TestClearButton:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("清除按钮测试")
        self.root.geometry("600x400")
        
        self.create_widgets()
    
    def create_widgets(self):
        # 搜索区域
        search_frame = ttk.LabelFrame(self.root, text="查找相似病例", padding=10)
        search_frame.pack(fill='x', pady=10, padx=10)
        
        # 姓名输入
        ttk.Label(search_frame, text="姓名:").grid(row=0, column=0, sticky='w', pady=2)
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=40)
        name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        
        # 性别输入
        ttk.Label(search_frame, text="性别:").grid(row=0, column=2, sticky='w', pady=2, padx=(10, 0))
        self.search_gender_var = tk.StringVar()
        gender_entry = ttk.Entry(search_frame, textvariable=self.search_gender_var, width=20)
        gender_entry.grid(row=0, column=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 住址输入
        ttk.Label(search_frame, text="住址:").grid(row=1, column=0, sticky='w', pady=2)
        self.search_address_var = tk.StringVar()
        address_entry = ttk.Entry(search_frame, textvariable=self.search_address_var, width=40)
        address_entry.grid(row=1, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 症状输入
        ttk.Label(search_frame, text="症状:").grid(row=2, column=0, sticky='w', pady=2)
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=40)
        symptoms_entry.grid(row=2, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        
        # 按钮框架
        button_frame = ttk.Frame(search_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))
        
        # 搜索按钮
        search_button = ttk.Button(button_frame, text="搜索病例", command=self.search_cases)
        search_button.pack(side='left', padx=(0, 10))
        
        # 清除按钮
        clear_button = ttk.Button(button_frame, text="清除", command=self.clear_search_fields)
        clear_button.pack(side='left')
        
        # 配置网格权重
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)
        
        # 测试数据填充按钮
        test_frame = ttk.LabelFrame(self.root, text="测试功能", padding=10)
        test_frame.pack(fill='x', pady=10, padx=10)
        
        ttk.Button(test_frame, text="填充测试数据", command=self.fill_test_data).pack(side='left', padx=5)
        ttk.Button(test_frame, text="显示当前数据", command=self.show_current_data).pack(side='left', padx=5)
        
        # 模拟搜索结果区域
        results_frame = ttk.LabelFrame(self.root, text="搜索结果（模拟）", padding=10)
        results_frame.pack(fill='both', expand=True, pady=10, padx=10)
        
        # 创建简单的列表框模拟搜索结果
        self.results_listbox = tk.Listbox(results_frame, height=8)
        self.results_listbox.pack(fill='both', expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.root, textvariable=self.status_var, relief='sunken')
        status_label.pack(fill='x', padx=10, pady=5)
    
    def search_cases(self):
        """模拟搜索功能"""
        name = self.search_name_var.get().strip()
        gender = self.search_gender_var.get().strip()
        address = self.search_address_var.get().strip()
        symptoms = self.symptoms_var.get().strip()
        
        if not (name or gender or address or symptoms):
            self.status_var.set("请至少输入一个搜索条件")
            return
        
        # 模拟添加搜索结果
        self.results_listbox.delete(0, 'end')
        
        # 添加一些模拟结果
        results = [
            f"结果1: {name or '未知'}, {gender or '未知'}, {address or '未知'}",
            f"结果2: 相似病例 - {symptoms or '无症状'}",
            f"结果3: 匹配项目..."
        ]
        
        for result in results:
            self.results_listbox.insert('end', result)
        
        self.status_var.set(f"找到 {len(results)} 个搜索结果")
    
    def clear_search_fields(self):
        """清除所有搜索字段"""
        self.search_name_var.set("")
        self.search_gender_var.set("")
        self.search_address_var.set("")
        self.symptoms_var.set("")
        
        # 清空搜索结果
        self.results_listbox.delete(0, 'end')
        
        # 更新状态
        self.status_var.set("已清除搜索条件和结果")
    
    def fill_test_data(self):
        """填充测试数据"""
        self.search_name_var.set("张三")
        self.search_gender_var.set("男")
        self.search_address_var.set("北京")
        self.symptoms_var.set("口渴")
        self.status_var.set("已填充测试数据")
    
    def show_current_data(self):
        """显示当前数据"""
        data = {
            "姓名": self.search_name_var.get(),
            "性别": self.search_gender_var.get(),
            "住址": self.search_address_var.get(),
            "症状": self.symptoms_var.get()
        }
        
        data_str = "\n".join([f"{k}: {v}" for k, v in data.items()])
        self.status_var.set(f"当前数据: {data_str}")
        print("当前搜索字段数据:")
        for k, v in data.items():
            print(f"  {k}: '{v}'")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestClearButton()
    app.run()
