#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV文件加载功能
"""

import os
import sys
import pandas as pd
from main_gui import MedicalRecordsManager

def test_csv_loading():
    """测试CSV文件加载功能"""
    print("开始测试CSV文件加载功能...")
    
    # 检查CSV文件是否存在
    csv_file = "ceshi/ceshi_real_data_converted.csv"
    if not os.path.exists(csv_file):
        print(f"错误：CSV文件 {csv_file} 不存在")
        return False
    
    print(f"找到CSV文件: {csv_file}")
    
    # 创建医疗记录管理器
    manager = MedicalRecordsManager("ceshi")
    
    # 测试加载CSV文件
    try:
        print("正在加载CSV文件...")
        success = manager.load_excel_files_with_progress()
        
        if success:
            print(f"✓ 成功加载文件")
            print(f"✓ 总共加载了 {len(manager.all_records)} 条记录")
            
            # 显示前几条记录的信息
            if manager.all_records:
                print("\n前5条记录预览:")
                for i, record in enumerate(manager.all_records[:5]):
                    print(f"记录 {i+1}:")
                    print(f"  姓名: {record.get('name', '未知')}")
                    print(f"  性别: {record.get('gender', '未知')}")
                    print(f"  年龄: {record.get('age', '未知')}")
                    print(f"  症状: {record.get('symptoms', '未知')[:50]}...")
                    print(f"  来源文件: {record.get('source_file', '未知')}")
                    print()
            
            # 显示错误信息（如果有）
            if manager.extraction_errors:
                print(f"\n警告：处理过程中遇到 {len(manager.extraction_errors)} 个问题:")
                for error in manager.extraction_errors:
                    print(f"  - {error}")
            
            return True
        else:
            print("✗ 加载失败")
            if manager.extraction_errors:
                print("错误信息:")
                for error in manager.extraction_errors:
                    print(f"  - {error}")
            return False
            
    except Exception as e:
        print(f"✗ 加载过程中出现异常: {str(e)}")
        return False

def test_csv_file_structure():
    """测试CSV文件结构"""
    print("\n检查CSV文件结构...")
    
    csv_file = "ceshi/ceshi_real_data_converted.csv"
    try:
        # 尝试不同编码读取
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        df = None
        used_encoding = None
        
        for encoding in encodings:
            try:
                df = pd.read_csv(csv_file, encoding=encoding)
                used_encoding = encoding
                break
            except UnicodeDecodeError:
                continue
        
        if df is None:
            print("✗ 无法读取CSV文件，编码格式不支持")
            return False
        
        print(f"✓ 成功读取CSV文件，使用编码: {used_encoding}")
        print(f"✓ 文件包含 {len(df)} 行数据")
        print(f"✓ 文件包含 {len(df.columns)} 列")
        
        print("\n列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. {col}")
        
        print(f"\n前3行数据预览:")
        print(df.head(3).to_string())
        
        return True
        
    except Exception as e:
        print(f"✗ 检查CSV文件时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CSV文件加载功能测试")
    print("=" * 60)
    
    # 测试CSV文件结构
    structure_ok = test_csv_file_structure()
    
    print("\n" + "=" * 60)
    
    # 测试CSV加载功能
    if structure_ok:
        loading_ok = test_csv_loading()
        
        if loading_ok:
            print("\n✓ 所有测试通过！CSV文件加载功能正常工作。")
        else:
            print("\n✗ CSV加载功能测试失败。")
    else:
        print("\n✗ CSV文件结构检查失败，跳过加载测试。")
    
    print("=" * 60)
