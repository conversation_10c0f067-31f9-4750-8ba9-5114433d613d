#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox

class TestSimplePaste:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("简单粘贴功能测试")
        self.root.geometry("600x500")
        
        self.create_widgets()
    
    def create_widgets(self):
        # 说明区域
        info_frame = ttk.LabelFrame(self.root, text="使用说明", padding=10)
        info_frame.pack(fill='x', pady=10, padx=10)
        
        info_text = """
1. 先复制一些文字到剪贴板（Ctrl+C）
2. 在下面的输入框中右键点击
3. 选择"粘贴"将剪贴板内容粘贴到输入框
4. 选择"清空"清空输入框内容
        """
        ttk.Label(info_frame, text=info_text, justify='left').pack()
        
        # 测试复制按钮
        button_frame = ttk.Frame(self.root)
        button_frame.pack(pady=10)
        
        ttk.But<PERSON>(button_frame, text="复制测试文字到剪贴板", 
                  command=self.copy_test_text).pack(side='left', padx=5)
        ttk.But<PERSON>(button_frame, text="显示剪贴板内容", 
                  command=self.show_clipboard).pack(side='left', padx=5)
        
        # 搜索区域测试
        search_frame = ttk.LabelFrame(self.root, text="查找相似病例（测试）", padding=10)
        search_frame.pack(fill='x', pady=10, padx=10)
        
        # 姓名输入
        ttk.Label(search_frame, text="姓名:").grid(row=0, column=0, sticky='w', pady=2)
        self.search_name_var = tk.StringVar()
        name_entry = ttk.Entry(search_frame, textvariable=self.search_name_var, width=40)
        name_entry.grid(row=0, column=1, sticky='ew', padx=(5, 0), pady=2)
        self.setup_simple_paste_menu(name_entry, self.search_name_var)
        
        # 性别输入
        ttk.Label(search_frame, text="性别:").grid(row=0, column=2, sticky='w', pady=2, padx=(10, 0))
        self.search_gender_var = tk.StringVar()
        gender_entry = ttk.Entry(search_frame, textvariable=self.search_gender_var, width=20)
        gender_entry.grid(row=0, column=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_simple_paste_menu(gender_entry, self.search_gender_var)
        
        # 住址输入
        ttk.Label(search_frame, text="住址:").grid(row=1, column=0, sticky='w', pady=2)
        self.search_address_var = tk.StringVar()
        address_entry = ttk.Entry(search_frame, textvariable=self.search_address_var, width=40)
        address_entry.grid(row=1, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_simple_paste_menu(address_entry, self.search_address_var)
        
        # 症状输入
        ttk.Label(search_frame, text="症状:").grid(row=2, column=0, sticky='w', pady=2)
        self.symptoms_var = tk.StringVar()
        symptoms_entry = ttk.Entry(search_frame, textvariable=self.symptoms_var, width=40)
        symptoms_entry.grid(row=2, column=1, columnspan=3, sticky='ew', padx=(5, 0), pady=2)
        self.setup_simple_paste_menu(symptoms_entry, self.symptoms_var)
        
        search_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(3, weight=1)
        
        # Text控件测试
        text_frame = ttk.LabelFrame(self.root, text="Text控件测试", padding=10)
        text_frame.pack(fill='both', expand=True, pady=10, padx=10)
        
        ttk.Label(text_frame, text="多行文本:").pack(anchor='w')
        self.test_text = tk.Text(text_frame, height=5, width=50)
        self.test_text.pack(fill='both', expand=True, pady=5)
        self.setup_text_paste_menu(self.test_text)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(self.root, textvariable=self.status_var, relief='sunken')
        status_label.pack(fill='x', padx=10, pady=5)
    
    def copy_test_text(self):
        """复制测试文字到剪贴板"""
        test_text = "张三"
        self.root.clipboard_clear()
        self.root.clipboard_append(test_text)
        self.status_var.set(f"已复制到剪贴板: {test_text}")
    
    def show_clipboard(self):
        """显示剪贴板内容"""
        try:
            content = self.root.clipboard_get()
            messagebox.showinfo("剪贴板内容", f"当前剪贴板内容：\n{content}")
        except tk.TclError:
            messagebox.showwarning("提示", "剪贴板为空")
    
    def setup_simple_paste_menu(self, widget, string_var):
        """为输入控件设置简单的粘贴右键菜单"""
        def show_paste_menu(event):
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            # 添加粘贴选项
            context_menu.add_command(
                label="粘贴",
                command=lambda: self.paste_from_clipboard(string_var)
            )
            
            # 添加清空选项
            context_menu.add_separator()
            context_menu.add_command(
                label="清空",
                command=lambda: string_var.set("")
            )
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        # 绑定右键事件
        widget.bind("<Button-3>", show_paste_menu)
    
    def setup_text_paste_menu(self, text_widget):
        """为Text控件设置简单的粘贴右键菜单"""
        def show_text_paste_menu(event):
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            # 添加粘贴选项
            context_menu.add_command(
                label="粘贴",
                command=lambda: self.paste_to_text_widget(text_widget)
            )
            
            # 添加清空选项
            context_menu.add_separator()
            context_menu.add_command(
                label="清空",
                command=lambda: text_widget.delete('1.0', 'end')
            )
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        # 绑定右键事件
        text_widget.bind("<Button-3>", show_text_paste_menu)
    
    def paste_from_clipboard(self, string_var):
        """从剪贴板粘贴内容到指定的StringVar"""
        try:
            # 获取剪贴板内容
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                # 清理内容（去除首尾空白字符）
                cleaned_content = clipboard_content.strip()
                # 设置到对应的变量
                string_var.set(cleaned_content)
                self.status_var.set(f"已粘贴: {cleaned_content}")
        except tk.TclError:
            # 剪贴板为空或无法访问
            messagebox.showwarning("提示", "剪贴板为空或无法访问")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴时出错: {str(e)}")
    
    def paste_to_text_widget(self, text_widget):
        """从剪贴板粘贴内容到Text控件"""
        try:
            # 获取剪贴板内容
            clipboard_content = self.root.clipboard_get()
            if clipboard_content:
                # 清理内容（去除首尾空白字符）
                cleaned_content = clipboard_content.strip()
                # 清空原内容并插入新内容
                text_widget.delete('1.0', 'end')
                text_widget.insert('1.0', cleaned_content)
                self.status_var.set(f"已粘贴到Text控件: {cleaned_content}")
        except tk.TclError:
            # 剪贴板为空或无法访问
            messagebox.showwarning("提示", "剪贴板为空或无法访问")
        except Exception as e:
            messagebox.showerror("错误", f"粘贴时出错: {str(e)}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestSimplePaste()
    app.run()
