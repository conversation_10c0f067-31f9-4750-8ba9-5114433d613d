#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列映射功能
"""

import pandas as pd
from main_gui import MedicalRecordsManager

def test_column_mapping():
    """测试CSV文件的列映射"""
    print("测试CSV文件列映射...")
    
    # 读取CSV文件
    csv_file = "ceshi/ceshi_real_data_converted.csv"
    
    try:
        # 尝试读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8')
        print(f"✓ 成功读取CSV文件")
        print(f"✓ 文件包含 {len(df)} 行数据")
        print(f"✓ 文件包含 {len(df.columns)} 列")
        
        print("\nCSV文件的列名:")
        for i, col in enumerate(df.columns):
            print(f"  {i+1}. '{col}'")
        
        # 创建医疗记录管理器来测试列映射
        manager = MedicalRecordsManager("ceshi")
        
        # 测试列映射
        columns = df.columns.tolist()
        mapping = manager.find_column_mapping(columns)
        
        print(f"\n列映射结果:")
        for field, col_name in mapping.items():
            print(f"  {field} -> '{col_name}'")
        
        # 检查关键字段是否被正确映射
        expected_fields = ['name', 'symptoms', 'prescription', 'usage', 'visit_time']
        missing_fields = []
        
        for field in expected_fields:
            if field not in mapping:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n⚠️  警告：以下字段未被映射: {missing_fields}")
        else:
            print(f"\n✓ 所有关键字段都已正确映射")
        
        # 显示前几行数据的映射结果
        print(f"\n前3行数据的映射结果:")
        for i, (_, row) in enumerate(df.head(3).iterrows()):
            print(f"\n记录 {i+1}:")
            for field, col_name in mapping.items():
                if col_name in df.columns:
                    value = row[col_name]
                    if pd.isna(value):
                        value = ""
                    else:
                        value = str(value).strip()
                    print(f"  {field}: '{value[:50]}{'...' if len(str(value)) > 50 else ''}'")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("CSV文件列映射测试")
    print("=" * 60)
    
    success = test_column_mapping()
    
    if success:
        print(f"\n✓ 测试完成")
    else:
        print(f"\n✗ 测试失败")
    
    print("=" * 60)
