@echo off
chcp 65001 > nul
title 医疗系统运行库问题一键解决工具

echo ╔════════════════════════════════════════════════╗
echo ║         🔧 运行库问题一键解决工具              ║
echo ║         专为Windows 7用户优化                 ║
echo ╚════════════════════════════════════════════════╝
echo.
echo 📋 使用说明：
echo    1. 此工具会自动检测和安装所需运行库
echo    2. 请确保以管理员权限运行（右键"以管理员身份运行"）
echo    3. 安装过程可能需要几分钟，请耐心等待
echo    4. 完成后会自动尝试启动医疗系统程序
echo.
echo 按任意键开始检测和修复...
pause
echo.

:: 检查是否以管理员权限运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  警告：未以管理员权限运行
    echo 建议右键此文件，选择"以管理员身份运行"
    echo 继续运行可能会导致部分运行库安装失败
    echo.
    set /p "continue=是否继续？(Y/N): "
    if /i not "%continue%"=="Y" exit /b
    echo.
)

:: 检查系统版本
echo 📋 正在检查系统版本...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo 检测到系统版本: Windows %VERSION%

:: 检查网络连接
echo 📋 正在检查网络连接...
ping www.baidu.com -n 1 -w 3000 >nul 2>&1
if %errorlevel% equ 0 (
    set "network_available=true"
    echo ✅ 网络连接正常
) else (
    set "network_available=false"
    echo ⚠️  网络连接不可用，将使用离线模式
)

:: 检查已安装的运行库
echo 📋 正在检查已安装的运行库...
python runtime_check.py >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  需要安装运行库
    set "need_install=true"
) else (
    echo ✅ 运行库检查通过
    set "need_install=false"
)

:: 根据情况执行不同的解决方案
if "%need_install%"=="true" (
    echo.
    echo 🔧 开始解决运行库问题...
    
    if "%network_available%"=="true" (
        echo 📦 在线模式：下载并安装运行库
        call :download_and_install
    ) else (
        echo 💾 离线模式：使用预置运行库
        call :offline_install
    )
) else (
    echo ✅ 系统环境良好，无需安装运行库
    goto :test_program
)

:download_and_install
echo 正在下载运行库...
call download_libraries.bat
echo 正在安装运行库...
call install_libraries.bat
goto :test_program

:offline_install
echo 正在安装预置运行库...
if exist "runtime_libraries" (
    call install_libraries.bat
) else (
    echo ❌ 未找到预置运行库文件
    echo 请联系技术支持或在有网络的环境下重新运行
    pause
    exit /b 1
)
goto :test_program

:test_program
echo.
echo 🚀 正在测试医疗系统程序...
if exist "河南省漯河市弘济医院医疗诊疗信息管理系统.exe" (
    echo 尝试启动程序...
    start "" "河南省漯河市弘济医院医疗诊疗信息管理系统.exe"
    echo.
    echo ✅ 程序已启动！
    echo 如果程序正常显示，说明问题已解决
) else (
    echo ❌ 未找到程序文件
)

echo.
echo 🎉 解决方案执行完成！
echo 如果仍有问题，请查看"Windows7专用说明.txt"
pause 