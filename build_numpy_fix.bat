@echo off

echo Building Medical System with NumPy fix...

if not exist "main_gui.py" (
    echo ERROR: main_gui.py not found!
    pause
    exit /b 1
)

if not exist "ceshi_real data_converted.csv" (
    echo ERROR: CSV file not found!
    pause
    exit /b 1
)

echo Creating runtime check script...
echo import sys > runtime_check.py
echo import os >> runtime_check.py

echo Cleaning old files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
for %%f in (*.spec) do if exist "%%f" del /q "%%f"

echo Collecting DLLs...
if not exist "system_dlls" mkdir "system_dlls"
copy "C:\Windows\System32\ucrtbase.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\msvcp140.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\vcruntime140.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\api-ms-win-core-path-l1-1-0.dll" "system_dlls\" >nul 2>&1

echo Starting PyInstaller with NumPy fix...

pyinstaller ^
--onefile ^
--noconsole ^
--name "Medical_System" ^
--add-data "ceshi_real data_converted.csv;." ^
--add-data "runtime_check.py;." ^
--add-data "system_dlls;system_dlls" ^
--hidden-import pandas ^
--hidden-import numpy ^
--hidden-import numpy.core ^
--hidden-import numpy.core._multiarray_tests ^
--hidden-import numpy.random ^
--hidden-import numpy.random.common ^
--hidden-import numpy.random.bounded_integers ^
--hidden-import numpy.random.entropy ^
--hidden-import openpyxl ^
--hidden-import pypinyin ^
--hidden-import tkinter ^
--hidden-import tkinter.ttk ^
--hidden-import tkinter.filedialog ^
--hidden-import tkinter.messagebox ^
--hidden-import tkinter.scrolledtext ^
--hidden-import concurrent.futures ^
--hidden-import threading ^
--hidden-import json ^
--hidden-import datetime ^
--hidden-import difflib ^
--hidden-import glob ^
--hidden-import re ^
--hidden-import time ^
--hidden-import platform ^
--hidden-import ctypes ^
--hidden-import secrets ^
--collect-all pandas ^
--collect-all numpy ^
--exclude-module matplotlib ^
--exclude-module test ^
--exclude-module unittest ^
--noupx ^
main_gui.py

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Creating release package...
if not exist "release" mkdir "release"
copy "dist\Medical_System.exe" "release\" >nul
copy "runtime_check.py" "release\" >nul 2>&1

if not exist "release\records" mkdir "release\records"
if exist "records" xcopy "records\*" "release\records\" /s /y >nul 2>&1

echo Build completed successfully!
echo Executable location: release\Medical_System.exe

set /p "test_now=Test run now? (Y/N): "
if /i "%test_now%"=="Y" start "" "release\Medical_System.exe"

pause
