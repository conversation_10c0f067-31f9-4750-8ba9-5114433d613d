#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面缩放功能
"""

import tkinter as tk
from tkinter import ttk, messagebox

class ScalingTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("界面缩放测试")
        
        # 获取屏幕信息
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        print(f"屏幕分辨率: {screen_width}x{screen_height}")
        
        # 计算缩放比例
        self.base_width = 1366
        self.base_height = 768
        
        width_scale = screen_width / self.base_width
        height_scale = screen_height / self.base_height
        self.scale_factor = min(width_scale, height_scale)
        
        # 对于极小屏幕的特殊处理
        if screen_width <= 1024 or screen_height <= 600:
            self.scale_factor = min(screen_width / 1200, screen_height / 650)
            self.is_very_small = True
        else:
            self.is_very_small = False
            
        self.scale_factor = max(0.4, min(self.scale_factor, 2.0))
        
        print(f"缩放比例: {self.scale_factor:.2f}")
        print(f"极小屏幕模式: {self.is_very_small}")
        
        # 设置窗口大小
        if self.is_very_small:
            window_width = int(800 * self.scale_factor)
            window_height = int(500 * self.scale_factor)
        else:
            window_width = int(1000 * self.scale_factor)
            window_height = int(600 * self.scale_factor)
            
        # 确保不超过屏幕
        window_width = min(window_width, int(screen_width * 0.95))
        window_height = min(window_height, int(screen_height * 0.95))
        
        # 居中显示
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        self.create_test_ui()
        
    def scale_value(self, value):
        return int(value * self.scale_factor)
        
    def scale_font_size(self, base_size):
        scaled_size = int(base_size * self.scale_factor)
        return max(8, min(scaled_size, 24))
        
    def create_test_ui(self):
        # 标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=self.scale_value(10), pady=self.scale_value(5))
        
        title_label = ttk.Label(title_frame, text="界面缩放测试", 
                               font=('SimHei', self.scale_font_size(16), 'bold'))
        title_label.pack()
        
        # 信息显示
        info_frame = ttk.LabelFrame(self.root, text="屏幕信息", padding=self.scale_value(10))
        info_frame.pack(fill='x', padx=self.scale_value(10), pady=self.scale_value(5))
        
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        info_text = f"""屏幕分辨率: {screen_width}x{screen_height}
缩放比例: {self.scale_factor:.2f} ({self.scale_factor:.0%})
极小屏幕模式: {'是' if self.is_very_small else '否'}
窗口大小: {self.root.winfo_reqwidth()}x{self.root.winfo_reqheight()}"""
        
        info_label = ttk.Label(info_frame, text=info_text, font=('SimHei', self.scale_font_size(10)))
        info_label.pack(anchor='w')
        
        # 测试区域
        if self.is_very_small:
            # 极小屏幕使用标签页
            notebook = ttk.Notebook(self.root)
            notebook.pack(fill='both', expand=True, padx=self.scale_value(10), pady=self.scale_value(5))
            
            # 标签页1
            tab1 = ttk.Frame(notebook)
            notebook.add(tab1, text="测试1")
            self.create_test_content(tab1)
            
            # 标签页2
            tab2 = ttk.Frame(notebook)
            notebook.add(tab2, text="测试2")
            self.create_test_content2(tab2)
        else:
            # 正常屏幕使用左右分栏
            paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
            paned.pack(fill='both', expand=True, padx=self.scale_value(10), pady=self.scale_value(5))
            
            left_frame = ttk.Frame(paned)
            right_frame = ttk.Frame(paned)
            
            paned.add(left_frame, weight=1)
            paned.add(right_frame, weight=1)
            
            self.create_test_content(left_frame)
            self.create_test_content2(right_frame)
            
    def create_test_content(self, parent):
        frame = ttk.LabelFrame(parent, text="测试区域1", padding=self.scale_value(10))
        frame.pack(fill='both', expand=True)
        
        # 输入框
        for i in range(5):
            ttk.Label(frame, text=f"字段{i+1}:", font=('SimHei', self.scale_font_size(10))).grid(
                row=i, column=0, sticky='w', pady=self.scale_value(2))
            entry = ttk.Entry(frame, width=max(15, int(25 * self.scale_factor)))
            entry.grid(row=i, column=1, sticky='ew', padx=(self.scale_value(5), 0), pady=self.scale_value(2))
            
        frame.columnconfigure(1, weight=1)
        
    def create_test_content2(self, parent):
        frame = ttk.LabelFrame(parent, text="测试区域2", padding=self.scale_value(10))
        frame.pack(fill='both', expand=True)
        
        # 列表
        if self.is_very_small:
            columns = ('列1', '列2', '列3')
            widths = [60, 80, 100]
        else:
            columns = ('列1', '列2', '列3', '列4', '列5')
            widths = [60, 80, 100, 80, 120]
            
        tree = ttk.Treeview(frame, columns=columns, show='headings', 
                           height=max(4, int(8 * self.scale_factor)))
        
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=self.scale_value(widths[i]), minwidth=self.scale_value(40))
            
        tree.pack(fill='both', expand=True)
        
        # 添加测试数据
        for i in range(10):
            if self.is_very_small:
                values = (f'数据{i+1}', f'值{i+1}', f'测试{i+1}')
            else:
                values = (f'数据{i+1}', f'值{i+1}', f'测试{i+1}', f'项目{i+1}', f'内容{i+1}')
            tree.insert('', 'end', values=values)
            
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = ScalingTest()
    app.run()
