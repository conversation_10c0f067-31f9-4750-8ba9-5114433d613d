@echo off
setlocal enabledelayedexpansion

echo ====================================================
echo      Medical System Build Tool v2.2
echo ====================================================
echo.

echo Start time: %date% %time%
echo.

echo Step 1: Check environment...
echo --------------------------------

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)
echo Python OK

pyinstaller --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PyInstaller not found!
    echo Installing PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo PyInstaller installation failed!
        pause
        exit /b 1
    )
)
echo PyInstaller OK

echo.

echo Step 2: Check files...
echo --------------------------------

if not exist "main_gui.py" (
    echo ERROR: main_gui.py not found!
    pause
    exit /b 1
)
echo main_gui.py OK

set "csv_file=ceshi_real data_converted.csv"
if not exist "%csv_file%" (
    echo ERROR: CSV file not found!
    pause
    exit /b 1
)
echo CSV file OK

echo.

echo Step 3: Create runtime tools...
echo --------------------------------

echo import sys > runtime_check.py
echo import os >> runtime_check.py
echo import platform >> runtime_check.py
echo print^("System check completed"^) >> runtime_check.py

echo Runtime check script created

echo.

echo Step 4: Clean old files...
echo --------------------------------

if exist "dist" (
    echo Removing dist folder...
    rmdir /s /q "dist"
)

if exist "build" (
    echo Removing build folder...
    rmdir /s /q "build"
)

for %%f in (*.spec) do (
    if exist "%%f" (
        del /q "%%f"
    )
)

echo Old files cleaned

echo.

echo Step 5: Collect system DLLs...
echo --------------------------------

if not exist "system_dlls" mkdir "system_dlls"

echo Collecting essential system DLLs...
copy "C:\Windows\System32\ucrtbase.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\msvcp140.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\vcruntime140.dll" "system_dlls\" >nul 2>&1
copy "C:\Windows\System32\api-ms-win-core-path-l1-1-0.dll" "system_dlls\" >nul 2>&1

echo DLLs collected

echo.

echo Step 6: Start building...
echo --------------------------------

echo Building executable...

pyinstaller --onefile --noconsole --name "Medical_System" --add-data "%csv_file%;." --add-data "runtime_check.py;." --add-data "system_dlls;system_dlls" --hidden-import pandas --hidden-import openpyxl --hidden-import pypinyin --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.filedialog --hidden-import tkinter.messagebox --hidden-import tkinter.scrolledtext --hidden-import concurrent.futures --hidden-import threading --hidden-import json --hidden-import datetime --hidden-import difflib --hidden-import glob --hidden-import re --hidden-import time --hidden-import platform --hidden-import ctypes --hidden-import secrets --exclude-module matplotlib --exclude-module numpy.testing --noupx main_gui.py

if %errorlevel% neq 0 (
    echo.
    echo Build failed!
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.

echo Step 7: Create release package...
echo --------------------------------

set "exe_file=dist\Medical_System.exe"

if exist "%exe_file%" (
    echo Executable file generated successfully!
    echo File location: %exe_file%
    
    for %%i in ("%exe_file%") do (
        set "file_size=%%~zi"
        echo File size: !file_size! bytes
    )
) else (
    echo ERROR: Executable file not found!
    pause
    exit /b 1
)

if not exist "release" mkdir "release"
echo Release folder created

copy "%exe_file%" "release\" >nul
echo Executable copied to release folder

if not exist "release\records" mkdir "release\records"
echo Records folder created

copy "runtime_check.py" "release\" >nul 2>&1
echo Runtime tools copied

if exist "records" (
    xcopy "records\*" "release\records\" /s /y >nul 2>&1
    echo Sample data copied
)

echo Medical System v2.2 > "release\README.txt"
echo =================== >> "release\README.txt"
echo. >> "release\README.txt"
echo Usage Instructions: >> "release\README.txt"
echo 1. Run Medical_System.exe >> "release\README.txt"
echo 2. Load Excel/CSV files from records folder >> "release\README.txt"
echo. >> "release\README.txt"
echo Build time: %date% %time% >> "release\README.txt"

echo User manual created

echo.

echo ====================================================
echo                Build Complete!
echo ====================================================
echo.
echo Release package location: release folder
echo Package contents:
echo    - Medical_System.exe
echo    - runtime_check.py
echo    - records folder
echo    - README.txt
echo.

set /p "test_now=Test run the program now? (Y/N): "
if /i "%test_now%"=="Y" (
    echo.
    echo Starting program for testing...
    start "" "release\Medical_System.exe"
)

echo.
echo Completion time: %date% %time%
echo Thank you for using the Medical System Build Tool!
echo.
pause
