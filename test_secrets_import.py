#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 secrets 模块导入
用于验证打包时是否包含了 secrets 模块
"""

def test_secrets_import():
    """测试 secrets 模块是否可以正常导入"""
    try:
        import secrets
        print("✓ secrets 模块导入成功")
        
        # 测试一些基本功能
        random_bytes = secrets.token_bytes(16)
        print(f"✓ secrets.token_bytes() 工作正常: {len(random_bytes)} bytes")
        
        random_hex = secrets.token_hex(8)
        print(f"✓ secrets.token_hex() 工作正常: {random_hex}")
        
        random_url = secrets.token_urlsafe(16)
        print(f"✓ secrets.token_urlsafe() 工作正常: {random_url}")
        
        return True
        
    except ImportError as e:
        print(f"✗ secrets 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ secrets 模块功能测试失败: {e}")
        return False

def test_other_modules():
    """测试其他可能缺失的模块"""
    modules_to_test = [
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'pandas',
        'openpyxl',
        'pypinyin',
        'concurrent.futures',
        'threading',
        'json',
        'datetime',
        'difflib',
        'glob',
        're',
        'time',
        'platform',
        'sys',
        'secrets'
    ]
    
    print("\n=== 模块导入测试 ===")
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✓ {module_name}")
        except ImportError as e:
            print(f"✗ {module_name}: {e}")
            failed_modules.append(module_name)
        except Exception as e:
            print(f"? {module_name}: {e}")
    
    if failed_modules:
        print(f"\n失败的模块: {', '.join(failed_modules)}")
        return False
    else:
        print("\n所有模块导入成功！")
        return True

if __name__ == "__main__":
    print("=== 打包模块测试 ===")
    print("测试程序是否包含所有必要的模块...")
    print()
    
    # 测试 secrets 模块
    secrets_ok = test_secrets_import()
    
    # 测试其他模块
    modules_ok = test_other_modules()
    
    print("\n=== 测试结果 ===")
    if secrets_ok and modules_ok:
        print("✓ 所有测试通过！程序应该可以正常运行。")
    else:
        print("✗ 存在问题，可能需要重新打包。")
        if not secrets_ok:
            print("  - secrets 模块有问题")
        if not modules_ok:
            print("  - 其他模块有问题")
    
    print("\n如果在打包后的程序中运行此测试失败，")
    print("请检查 build_medical_system.bat 中的 --hidden-import 参数。")
