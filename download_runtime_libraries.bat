@echo off
chcp 65001 > nul
title 医疗系统运行库下载工具

echo ╔════════════════════════════════════════════════╗
echo ║          🔧 运行库下载和安装工具                ║
echo ║          解决所有运行库依赖问题                ║
echo ╚════════════════════════════════════════════════╝
echo.

:: 创建运行库下载目录
if not exist "runtime_libraries" mkdir "runtime_libraries"
echo ✅ 创建运行库下载目录

:: 运行库下载URL列表
echo 📋 准备下载以下运行库：
echo    • Microsoft Visual C++ Redistributable 2015-2022 (x64)
echo    • Microsoft Visual C++ Redistributable 2015-2022 (x86)
echo    • Microsoft Visual C++ Redistributable 2013 (x64)
echo    • Microsoft Visual C++ Redistributable 2013 (x86)
echo    • Microsoft Visual C++ Redistributable 2010 (x64)
echo    • Microsoft Visual C++ Redistributable 2010 (x86)
echo    • Microsoft Visual C++ Redistributable 2008 (x64)
echo    • Microsoft Visual C++ Redistributable 2008 (x86)
echo    • .NET Framework 4.8
echo    • Windows Universal C Runtime
echo.

:: 创建下载脚本
echo 📦 创建下载脚本...
(
echo @echo off
echo echo 正在下载运行库，请稍候...
echo.
echo echo 下载 Visual C++ 2015-2022 x64...
echo powershell -Command "Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x64.exe' -OutFile 'runtime_libraries\vc_redist_2015-2022_x64.exe'"
echo.
echo echo 下载 Visual C++ 2015-2022 x86...
echo powershell -Command "Invoke-WebRequest -Uri 'https://aka.ms/vs/17/release/vc_redist.x86.exe' -OutFile 'runtime_libraries\vc_redist_2015-2022_x86.exe'"
echo.
echo echo 下载 Visual C++ 2013 x64...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x64.exe' -OutFile 'runtime_libraries\vcredist_2013_x64.exe'"
echo.
echo echo 下载 Visual C++ 2013 x86...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe' -OutFile 'runtime_libraries\vcredist_2013_x86.exe'"
echo.
echo echo 下载 Visual C++ 2010 x64...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/1/6/5/165255E7-1014-4D0A-B094-B6A430A6BFFC/vcredist_x64.exe' -OutFile 'runtime_libraries\vcredist_2010_x64.exe'"
echo.
echo echo 下载 Visual C++ 2010 x86...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/1/6/5/165255E7-1014-4D0A-B094-B6A430A6BFFC/vcredist_x86.exe' -OutFile 'runtime_libraries\vcredist_2010_x86.exe'"
echo.
echo echo 下载 Visual C++ 2008 x64...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/5/D/8/5D8C65CB-C849-4025-8E95-C3966CAFD8AE/vcredist_x64.exe' -OutFile 'runtime_libraries\vcredist_2008_x64.exe'"
echo.
echo echo 下载 Visual C++ 2008 x86...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/5/D/8/5D8C65CB-C849-4025-8E95-C3966CAFD8AE/vcredist_x86.exe' -OutFile 'runtime_libraries\vcredist_2008_x86.exe'"
echo.
echo echo 下载 .NET Framework 4.8...
echo powershell -Command "Invoke-WebRequest -Uri 'https://download.microsoft.com/download/6/E/4/6E48E8AB-DC00-419E-9704-06DD46E5F81D/NDP48-x86-x64-AllOS-ENU.exe' -OutFile 'runtime_libraries\NDP48-x86-x64-AllOS-ENU.exe'"
echo.
echo echo 所有运行库下载完成！
echo pause
) > "download_libraries.bat"

:: 创建安装脚本
echo 📦 创建安装脚本...
(
echo @echo off
echo title 运行库安装程序
echo chcp 65001 ^> nul
echo.
echo echo ╔════════════════════════════════════════════════╗
echo echo ║              运行库安装程序                    ║
echo echo ╚════════════════════════════════════════════════╝
echo echo.
echo.
echo echo 🔧 正在安装运行库，请稍候...
echo echo 请在每个安装程序中点击"同意"和"安装"
echo echo.
echo.
echo echo 📋 安装 Visual C++ 2015-2022 x64...
echo if exist "runtime_libraries\vc_redist_2015-2022_x64.exe" (
echo     start /wait "runtime_libraries\vc_redist_2015-2022_x64.exe" /quiet
echo     echo ✅ Visual C++ 2015-2022 x64 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2015-2022 x64 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2015-2022 x86...
echo if exist "runtime_libraries\vc_redist_2015-2022_x86.exe" (
echo     start /wait "runtime_libraries\vc_redist_2015-2022_x86.exe" /quiet
echo     echo ✅ Visual C++ 2015-2022 x86 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2015-2022 x86 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2013 x64...
echo if exist "runtime_libraries\vcredist_2013_x64.exe" (
echo     start /wait "runtime_libraries\vcredist_2013_x64.exe" /quiet
echo     echo ✅ Visual C++ 2013 x64 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2013 x64 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2013 x86...
echo if exist "runtime_libraries\vcredist_2013_x86.exe" (
echo     start /wait "runtime_libraries\vcredist_2013_x86.exe" /quiet
echo     echo ✅ Visual C++ 2013 x86 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2013 x86 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2010 x64...
echo if exist "runtime_libraries\vcredist_2010_x64.exe" (
echo     start /wait "runtime_libraries\vcredist_2010_x64.exe" /quiet
echo     echo ✅ Visual C++ 2010 x64 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2010 x64 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2010 x86...
echo if exist "runtime_libraries\vcredist_2010_x86.exe" (
echo     start /wait "runtime_libraries\vcredist_2010_x86.exe" /quiet
echo     echo ✅ Visual C++ 2010 x86 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2010 x86 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2008 x64...
echo if exist "runtime_libraries\vcredist_2008_x64.exe" (
echo     start /wait "runtime_libraries\vcredist_2008_x64.exe" /quiet
echo     echo ✅ Visual C++ 2008 x64 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2008 x64 安装文件
echo )
echo.
echo echo 📋 安装 Visual C++ 2008 x86...
echo if exist "runtime_libraries\vcredist_2008_x86.exe" (
echo     start /wait "runtime_libraries\vcredist_2008_x86.exe" /quiet
echo     echo ✅ Visual C++ 2008 x86 安装完成
echo ) else (
echo     echo ❌ 未找到 Visual C++ 2008 x86 安装文件
echo )
echo.
echo echo 📋 安装 .NET Framework 4.8...
echo if exist "runtime_libraries\NDP48-x86-x64-AllOS-ENU.exe" (
echo     start /wait "runtime_libraries\NDP48-x86-x64-AllOS-ENU.exe" /quiet
echo     echo ✅ .NET Framework 4.8 安装完成
echo ) else (
echo     echo ❌ 未找到 .NET Framework 4.8 安装文件
echo )
echo.
echo echo ╔════════════════════════════════════════════════╗
echo echo ║           🎉 所有运行库安装完成！               ║
echo echo ║           请重启计算机以确保生效               ║
echo echo ╚════════════════════════════════════════════════╝
echo echo.
echo echo 是否立即重启计算机？
echo set /p "restart=请输入 Y 立即重启，N 稍后重启: "
echo if /i "%restart%"=="Y" (
echo     echo 正在重启计算机...
echo     shutdown /r /t 10 /c "安装运行库后重启系统"
echo ) else (
echo     echo 请稍后手动重启计算机以确保运行库生效
echo )
echo.
echo pause
) > "install_libraries.bat"

echo ✅ 运行库下载和安装脚本创建完成
echo.

echo 📋 使用说明：
echo   1. 运行 download_libraries.bat 下载所有运行库
echo   2. 运行 install_libraries.bat 安装所有运行库
echo   3. 重启计算机
echo   4. 运行医疗系统程序
echo.

pause 