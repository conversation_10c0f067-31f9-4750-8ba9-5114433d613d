# 启动背景图片使用说明

## 功能说明
软件现在支持在启动时显示自定义背景图片，让启动画面更加美观和个性化。

## 支持的图片格式
- PNG格式（推荐）
- JPG/JPEG格式
- GIF格式

## 图片文件名要求
程序会按以下顺序查找背景图片文件：
1. `background.png`
2. `background.jpg`
3. `background.jpeg`
4. `bg.png`
5. `bg.jpg`

## 图片放置位置
将背景图片文件放在与 `main_gui.py` 相同的目录下，即：
```
项目文件夹/
├── main_gui.py
├── background.png  (您的背景图片)
└── 其他文件...
```

## 图片尺寸建议
- 推荐尺寸：600x400 像素
- 程序会自动调整图片大小以适应启动窗口
- 支持任意尺寸的图片，但建议使用16:10或4:3的宽高比

## Logo图片支持
除了背景图片，还支持在启动画面上显示Logo：
- 支持的Logo文件名：`logo.png`, `logo.jpg`, `icon.png`, `icon.jpg`
- Logo会显示在启动画面的顶部
- 如果没有Logo文件，会显示默认的医院图标

## 使用步骤
1. 准备您的背景图片（建议600x400像素的PNG格式）
2. 将图片重命名为 `background.png`
3. 将图片文件复制到与 `main_gui.py` 相同的目录
4. 运行程序，启动时就会显示您的背景图片

## 注意事项
- 如果没有找到背景图片文件，程序会使用默认的白色背景
- 背景图片会自动调整大小，不会变形
- 文字颜色会根据是否有背景图片自动调整（有背景图时文字为白色，无背景图时为深色）
- 建议使用色彩不太鲜艳的背景图片，以确保文字清晰可读

## 打包说明
如果您使用PyInstaller打包程序，需要确保背景图片文件被包含在打包文件中。可以在打包时添加 `--add-data` 参数：
```bash
pyinstaller --add-data "background.png;." main_gui.py
```

现在您可以上传您的背景图片了！
