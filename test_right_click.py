#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk

class TestRightClick:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("右键菜单测试")
        self.root.geometry("400x300")
        
        # 模拟复制的病例数据
        self.copied_case_data = {
            'name': '张三',
            'age': '50',
            'gender': '男',
            'address': '北京',
            'contact': '13456789012',
            'symptoms': '口渴',
            'diagnosis': '糖尿病',
            'prescription': '吃点抗糖药',
            'usage': '一天三次，内服。',
            'prescriber': '未知'
        }
        
        self.create_widgets()
    
    def create_widgets(self):
        # 创建几个测试输入框
        ttk.Label(self.root, text="姓名:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        self.name_entry = ttk.Entry(self.root, width=30)
        self.name_entry.grid(row=0, column=1, padx=10, pady=5)
        self.setup_right_click_menu(self.name_entry, 'name_var')
        
        ttk.Label(self.root, text="年龄:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        self.age_entry = ttk.Entry(self.root, width=30)
        self.age_entry.grid(row=1, column=1, padx=10, pady=5)
        self.setup_right_click_menu(self.age_entry, 'age_var')
        
        ttk.Label(self.root, text="症状:").grid(row=2, column=0, sticky='nw', padx=10, pady=5)
        self.symptoms_text = tk.Text(self.root, height=5, width=30)
        self.symptoms_text.grid(row=2, column=1, padx=10, pady=5)
        self.setup_right_click_menu(self.symptoms_text, 'symptoms_add_var')
        
        # 说明文字
        info_label = ttk.Label(self.root, text="右键点击输入框查看粘贴菜单", foreground='blue')
        info_label.grid(row=3, column=0, columnspan=2, pady=20)
    
    def setup_right_click_menu(self, widget, field_name):
        """为输入控件设置右键菜单"""
        def show_context_menu(event):
            # 只有在有复制数据时才显示菜单
            if not self.copied_case_data:
                return
            
            # 创建右键菜单
            context_menu = tk.Menu(self.root, tearoff=0)
            
            # 根据字段类型添加相应的粘贴选项
            field_mapping = {
                'name_var': ('姓名', 'name'),
                'age_var': ('年龄', 'age'),
                'symptoms_add_var': ('症状', 'symptoms')
            }
            
            if field_name in field_mapping:
                field_display_name, field_key = field_mapping[field_name]
                field_value = self.copied_case_data.get(field_key, '')
                
                if field_value:
                    context_menu.add_command(
                        label=f"粘贴{field_display_name}: {field_value[:20]}{'...' if len(str(field_value)) > 20 else ''}",
                        command=lambda: self.paste_field_data(widget, field_name, field_key)
                    )
            
            # 添加粘贴所有信息的选项
            context_menu.add_separator()
            context_menu.add_command(
                label="粘贴所有病例信息",
                command=lambda: self.paste_all_case_data()
            )
            
            # 显示菜单
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()
        
        # 绑定右键事件
        widget.bind("<Button-3>", show_context_menu)  # Windows右键
    
    def paste_field_data(self, widget, field_name, field_key):
        """粘贴单个字段的数据"""
        if not self.copied_case_data:
            return
        
        field_value = self.copied_case_data.get(field_key, '')
        if not field_value:
            return
        
        try:
            # 根据控件类型进行不同的处理
            if isinstance(widget, tk.Text):
                # Text控件
                widget.delete('1.0', 'end')
                widget.insert('1.0', str(field_value))
            elif isinstance(widget, ttk.Entry):
                # Entry控件
                widget.delete(0, 'end')
                widget.insert(0, str(field_value))
                        
        except Exception as e:
            print(f"粘贴数据时出错: {str(e)}")
    
    def paste_all_case_data(self):
        """粘贴所有病例数据"""
        if not self.copied_case_data:
            return
        
        try:
            # 填充所有字段
            self.name_entry.delete(0, 'end')
            self.name_entry.insert(0, self.copied_case_data.get('name', ''))
            
            self.age_entry.delete(0, 'end')
            self.age_entry.insert(0, self.copied_case_data.get('age', ''))
            
            self.symptoms_text.delete('1.0', 'end')
            self.symptoms_text.insert('1.0', self.copied_case_data.get('symptoms', ''))
            
        except Exception as e:
            print(f"粘贴所有数据时出错: {str(e)}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = TestRightClick()
    app.run()
