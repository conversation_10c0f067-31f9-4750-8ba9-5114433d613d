医疗系统运行库问题解决指南
===============================

如果程序启动时出现以下错误：
- "Failed to load Python DLL"
- "无法定位程序输入点"
- 程序无法启动或闪退

请按以下步骤操作：

步骤1：解压文件
------------------
将收到的release文件夹完整解压到任意位置（建议桌面）

步骤2：运行修复工具
------------------
1. 找到"一键解决运行库问题.bat"文件
2. 右键点击，选择"以管理员身份运行"
3. 按照提示完成自动检测和安装
4. 等待安装完成（可能需要几分钟）

步骤3：启动程序
------------------
安装完成后，双击"Medical_System.exe"启动程序

故障排除：
------------------
Q: 提示需要管理员权限怎么办？
A: 右键bat文件，选择"以管理员身份运行"

Q: 网络连接失败怎么办？
A: 工具会自动切换到离线模式，使用预置的运行库文件

Q: 安装后仍然有问题怎么办？
A: 1. 重启计算机后再试
   2. 检查是否Windows 7系统（需要特殊处理）
   3. 联系技术支持

技术支持：
------------------
如需帮助，请提供：
- 操作系统版本（Windows 7/8/10/11）
- 具体错误截图
- 是否成功运行了一键解决工具 