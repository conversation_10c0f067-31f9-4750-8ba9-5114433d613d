# converter.py
# 功能：高性能大文件转换脚本，专门处理包含大量sheet的Excel文件
# 作者：AI Assistant
# 版本：4.0 (大文件优化版)

import os
import sys
import time
import shutil
import pandas as pd
import win32com.client as win32
import pythoncom
import re
from datetime import datetime

class ExcelConverter:
    def __init__(self, batch_size=100):
        """
        初始化转换器
        Args:
            batch_size (int): 批处理大小，每处理多少个sheet写入一次文件
        """
        self.batch_size = batch_size
        self.processed_count = 0
        self.total_records = 0
        self.start_time = None
        self.temp_records = []
        
    def extract_patient_data_from_sheet(self, sheet_data, source_excel, sheet_name):
        """
        根据病历卡格式，从单个工作表的数据中提取就诊记录
        """
        all_visits = []
        
        # 检查数据有效性
        if not sheet_data or not isinstance(sheet_data, list) or len(sheet_data) < 2:
            return []

        # 提取第一行的病人基本信息
        base_info = {}
        first_row = sheet_data[0]
        try:
            base_info['姓名'] = str(first_row[0]).strip() if len(first_row) > 0 and first_row[0] else ''
            base_info['性别'] = str(first_row[1]).strip() if len(first_row) > 1 and first_row[1] else ''
            base_info['年龄'] = str(first_row[2]).strip() if len(first_row) > 2 and first_row[2] else ''
            base_info['医保状态'] = str(first_row[3]).strip() if len(first_row) > 3 and first_row[3] else ''
            base_info['联系方式'] = str(first_row[4]).strip() if len(first_row) > 4 and first_row[4] else ''
        except (IndexError, AttributeError):
            return []

        if not base_info.get('姓名'):
            return []

        # 解析多次就诊记录
        current_visit = None
        for row_idx, row in enumerate(sheet_data[1:], start=1):
            if not row or not any(cell for cell in row if cell):
                continue

            first_cell = str(row[0]).strip() if row[0] else ""
            date_match = re.match(r'^\d{4}[./-]\d{1,2}[./-]\d{1,2}$', first_cell)

            if date_match:
                # 保存前一次就诊记录
                if current_visit:
                    current_visit['处方'] = '\n'.join(current_visit.get('处方_list', []))
                    current_visit.pop('处方_list', None)
                    all_visits.append(current_visit)

                # 开始新的就诊记录
                current_visit = base_info.copy()
                current_visit['就诊时间'] = date_match.group(0)
                symptoms = [str(cell).strip() for cell in row[1:] if cell]
                current_visit['症状诊断'] = ' '.join(symptoms)
                current_visit['处方_list'] = []
                current_visit['来源文件'] = f"{source_excel}#{sheet_name}"

            elif current_visit:
                # 处方行
                prescription_line = [str(cell).strip() for cell in row if cell]
                if prescription_line:
                    current_visit['处方_list'].append('   '.join(prescription_line))

        # 保存最后一次就诊记录
        if current_visit:
            current_visit['处方'] = '\n'.join(current_visit.get('处方_list', []))
            current_visit.pop('处方_list', None)
            all_visits.append(current_visit)

        return all_visits

    def save_batch_to_csv(self, output_file, records, is_first_batch=False):
        """
        将一批记录保存到CSV文件
        """
        if not records:
            return
            
        df = pd.DataFrame(records)
        column_order = ['姓名', '性别', '年龄', '就诊时间', '症状诊断', '处方', '医保状态', '联系方式', '来源文件']
        df = df.reindex(columns=column_order)
        
        # 第一批写入时包含标题，后续批次追加不含标题
        mode = 'w' if is_first_batch else 'a'
        header = is_first_batch
        
        df.to_csv(output_file, index=False, encoding='utf-8-sig', mode=mode, header=header)
        
    def estimate_remaining_time(self, processed, total):
        """
        估算剩余处理时间
        """
        if processed == 0:
            return "计算中..."
            
        elapsed = time.time() - self.start_time
        rate = processed / elapsed
        remaining = (total - processed) / rate
        
        hours = int(remaining // 3600)
        minutes = int((remaining % 3600) // 60)
        seconds = int(remaining % 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"

    def print_progress(self, current, total, records_count):
        """
        打印详细进度信息
        """
        percentage = (current / total) * 100
        remaining_time = self.estimate_remaining_time(current, total)
        elapsed = time.time() - self.start_time
        
        progress_bar = "█" * int(percentage // 2) + "░" * (50 - int(percentage // 2))
        
        print(f"\r[{progress_bar}] {percentage:.2f}% | "
              f"Sheet: {current}/{total} | "
              f"记录: {records_count} | "
              f"已用时: {elapsed:.0f}s | "
              f"预计剩余: {remaining_time}", end="")

    def convert_excel_file(self, file_path):
        """
        主转换函数 - 优化版本
        """
        if not os.path.isfile(file_path):
            print(f"错误：文件不存在 -> {file_path}")
            return False

        print(f"开始处理大文件: {os.path.basename(file_path)}")
        print(f"批处理大小: {self.batch_size} sheets")
        self.start_time = time.time()

        # 准备输出文件
        dir_name = os.path.dirname(file_path)
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        output_file = os.path.join(dir_name, f"{base_name}_converted.csv")
        
        # 如果输出文件已存在，先备份
        if os.path.exists(output_file):
            backup_name = f"{output_file}.backup_{int(time.time())}"
            shutil.move(output_file, backup_name)
            print(f"原有输出文件已备份为: {os.path.basename(backup_name)}")

        # 初始化 COM
        pythoncom.CoInitialize()
        excel_app = None
        is_first_batch = True
        
        try:
            # 启动 Excel
            print("\n正在启动 Excel 应用...")
            excel_app = win32.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False

            print("正在打开工作簿... (大文件可能需要较长时间)")
            workbook = excel_app.Workbooks.Open(os.path.abspath(file_path), ReadOnly=True)
            
            total_sheets = workbook.Worksheets.Count
            print(f"\n文件打开成功！")
            print(f"发现 {total_sheets} 个工作表")
            print(f"预计处理时间: 约 {total_sheets // 10} 分钟 (取决于数据复杂度)")
            print("=" * 70)

            for i in range(1, total_sheets + 1):
                try:
                    sheet = workbook.Worksheets(i)
                    sheet_data_tuple = sheet.UsedRange.Value
                    
                    if sheet_data_tuple:
                        # 安全地转换数据格式
                        if isinstance(sheet_data_tuple[0], (list, tuple)):
                            sheet_data_list = [list(row) for row in sheet_data_tuple]
                        else:
                            # 单行数据的特殊处理
                            sheet_data_list = [list(sheet_data_tuple)]
                            
                        sheet_records = self.extract_patient_data_from_sheet(
                            sheet_data_list, os.path.basename(file_path), sheet.Name
                        )
                        
                        if sheet_records:
                            self.temp_records.extend(sheet_records)
                            self.total_records += len(sheet_records)

                except Exception as e:
                    print(f"\n警告: Sheet {i} 处理失败 ({e})，已跳过")
                    continue

                self.processed_count = i
                
                # 显示进度
                self.print_progress(i, total_sheets, self.total_records)
                
                # 批量保存
                if len(self.temp_records) >= self.batch_size or i == total_sheets:
                    if self.temp_records:
                        self.save_batch_to_csv(output_file, self.temp_records, is_first_batch)
                        is_first_batch = False
                        self.temp_records.clear()  # 释放内存

            workbook.Close(SaveChanges=False)
            print(f"\n\n✅ 数据提取完成！")
            print(f"📊 总计处理了 {total_sheets} 个工作表")
            print(f"📝 提取了 {self.total_records} 条就诊记录")

        except Exception as e:
            print(f"\n❌ 处理过程中发生错误: {e}")
            print("\n请检查:")
            print("1. Microsoft Excel 是否已正确安装")
            print("2. 文件是否被其他程序占用")
            print("3. 磁盘空间是否充足")
            print("4. 文件是否损坏")
            return False
            
        finally:
            if excel_app:
                excel_app.Quit()
            pythoncom.CoUninitialize()

        # 最终统计
        if self.total_records > 0:
            print(f"\n🎉 转换成功完成！")
            print(f"📄 输出文件: {output_file}")
            print(f"📊 文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
            
            # 备份原始文件
            try:
                backup_dir = os.path.join(dir_name, "backup_originals")
                os.makedirs(backup_dir, exist_ok=True)
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                if not os.path.exists(backup_path):
                    shutil.copy2(file_path, backup_path)
                    print(f"📁 原始文件已备份到: {backup_dir}")
            except Exception as e:
                print(f"⚠️  备份原始文件失败: {e}")

        else:
            print("\n❌ 未提取到任何有效数据")
            return False

        total_time = time.time() - self.start_time
        print(f"⏱️  总耗时: {total_time / 60:.2f} 分钟")
        print(f"⚡ 平均速度: {total_sheets / total_time:.2f} sheets/秒")
        
        return True


# ==============================================================================
# --- 程序主入口 ---
# ==============================================================================
if __name__ == "__main__":
    # --- 请在这里修改为您文件的实际完整路径 ---
    # 使用 r'' 的格式可以避免Windows路径中反斜杠 \ 的转义问题
    # 直接从文件属性中复制路径，然后粘贴到下面的引号中即可
    
    FILE_PATH_TO_CONVERT = r'E:\code\yiyaoxinxichuli\records\real data.xlsx'  # <--- 修改这里
    
    # 可调参数
    BATCH_SIZE = 100  # 每批处理的sheet数量，可根据内存大小调整
    
    # ----------------------------------------------------
    
    print("=" * 70)
    print("🚀 医疗数据高性能转换工具 v4.0")
    print("=" * 70)
    print(f"📂 目标文件: {FILE_PATH_TO_CONVERT}")
    print(f"⚙️  批处理大小: {BATCH_SIZE}")
    print("=" * 70)
    
    converter = ExcelConverter(batch_size=BATCH_SIZE)
    success = converter.convert_excel_file(FILE_PATH_TO_CONVERT)
    
    if success:
        print("\n🎊 转换任务已完成！您现在可以使用生成的CSV文件进行后续处理。")
    else:
        print("\n💥 转换失败，请检查错误信息并重试。")
    
    input("\n按 Enter 键退出...")